import json
import requests
import re
import html
from datetime import datetime

def parse_deal_html_response(html_content, search_term):
    """
    Parse HTML response to extract deal information
    """
    try:
        parsed_data = {
            'search_term': search_term,
            'deals_found': [],
            'total_deals': 0,
            'parsing_timestamp': datetime.now().isoformat(),
            'raw_html_length': len(html_content)
        }

        # Look for deal tables or lists
        table_patterns = [
            r'<table[^>]*>(.*?)</table>',
            r'<tbody[^>]*>(.*?)</tbody>',
            r'<div[^>]*class[^>]*deal[^>]*>(.*?)</div>'
        ]

        for pattern in table_patterns:
            tables = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for table in tables:
                # Extract rows
                rows = re.findall(r'<tr[^>]*>(.*?)</tr>', table, re.IGNORECASE | re.DOTALL)
                for row in rows:
                    cells = re.findall(r'<td[^>]*>(.*?)</td>', row, re.IGNORECASE | re.DOTALL)
                    if cells and len(cells) > 1:
                        # Clean cell content
                        clean_cells = [html.unescape(re.sub(r'<[^>]+>', '', cell).strip()) for cell in cells]
                        if any(search_term in cell for cell in clean_cells):
                            parsed_data['deals_found'].append({
                                'cells': clean_cells,
                                'raw_row': row[:200] + '...' if len(row) > 200 else row
                            })

        # Look for deal numbers in various formats
        deal_patterns = [
            rf'deal[^>]*{search_term}[^<]*',
            rf'{search_term}[^<]*deal[^<]*',
            rf'number[^>]*{search_term}[^<]*',
            rf'{search_term}[^<]*number[^<]*'
        ]

        for pattern in deal_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                clean_match = html.unescape(re.sub(r'<[^>]+>', '', match).strip())
                if clean_match and clean_match not in [d.get('text', '') for d in parsed_data['deals_found']]:
                    parsed_data['deals_found'].append({
                        'text': clean_match,
                        'type': 'pattern_match'
                    })

        parsed_data['total_deals'] = len(parsed_data['deals_found'])
        return parsed_data

    except Exception as e:
        return {
            'error': f'Failed to parse HTML: {str(e)}',
            'search_term': search_term
        }

def parse_deal_table_response(table_content, search_term):
    """
    Parse table-specific HTML response to extract deal information
    """
    try:
        parsed_data = {
            'search_term': search_term,
            'table_data': [],
            'headers': [],
            'matching_rows': [],
            'parsing_timestamp': datetime.now().isoformat()
        }

        # Extract table headers
        header_match = re.search(r'<thead[^>]*>(.*?)</thead>', table_content, re.IGNORECASE | re.DOTALL)
        if header_match:
            header_cells = re.findall(r'<th[^>]*>(.*?)</th>', header_match.group(1), re.IGNORECASE | re.DOTALL)
            parsed_data['headers'] = [html.unescape(re.sub(r'<[^>]+>', '', cell).strip()) for cell in header_cells]

        # Extract all table rows
        rows = re.findall(r'<tr[^>]*>(.*?)</tr>', table_content, re.IGNORECASE | re.DOTALL)

        for i, row in enumerate(rows):
            cells = re.findall(r'<td[^>]*>(.*?)</td>', row, re.IGNORECASE | re.DOTALL)
            if cells:
                clean_cells = [html.unescape(re.sub(r'<[^>]+>', '', cell).strip()) for cell in cells]
                row_data = {
                    'row_index': i,
                    'cells': clean_cells,
                    'contains_search_term': any(search_term in cell for cell in clean_cells)
                }

                parsed_data['table_data'].append(row_data)

                if row_data['contains_search_term']:
                    parsed_data['matching_rows'].append(row_data)

        return parsed_data

    except Exception as e:
        return {
            'error': f'Failed to parse table: {str(e)}',
            'search_term': search_term
        }

def test_deal_search():
    """
    Test function specifically for deal ID 198275
    """
    test_event = {
        'username': 'your_username_here',  # Replace with actual username
        'password': 'your_password_here',  # Replace with actual password
        'perform_search': True,
        'search_term': '198275',
        'return_html': False,
        'search_elements': True
    }

    print("Testing deal search for ID: 198275")
    print("=" * 50)

    # Call the main lambda handler
    result = lambda_handler(test_event, None)

    # Parse and display results
    if result['statusCode'] == 200:
        body = json.loads(result['body'])

        print(f"Login Status: {'✅ Success' if body.get('is_logged_in') else '❌ Failed'}")
        print(f"Search Performed: {'✅ Yes' if body.get('search_results', {}).get('success') else '❌ No'}")

        search_results = body.get('search_results', {})
        if search_results.get('success'):
            print(f"Search Method: {search_results.get('method', 'Unknown')}")
            print(f"Response Type: {search_results.get('response_type', 'Unknown')}")
            print(f"Response Length: {search_results.get('response_length', 0)} characters")

            # Display parsed data if available
            if 'parsed_html_data' in search_results:
                html_data = search_results['parsed_html_data']
                print(f"Deals Found: {html_data.get('total_deals', 0)}")
                for i, deal in enumerate(html_data.get('deals_found', [])[:3]):  # Show first 3
                    print(f"  Deal {i+1}: {deal}")

            if 'parsed_table_data' in search_results:
                table_data = search_results['parsed_table_data']
                print(f"Table Rows: {len(table_data.get('table_data', []))}")
                print(f"Matching Rows: {len(table_data.get('matching_rows', []))}")
                for i, row in enumerate(table_data.get('matching_rows', [])[:2]):  # Show first 2
                    print(f"  Row {i+1}: {row['cells']}")

        else:
            print(f"Search Error: {search_results.get('error', 'Unknown error')}")

    else:
        print(f"Lambda Error: {result}")

    return result

def lambda_handler(event, context):
    """
    Enhanced Lambda function to login to Georgia DLR portal and access dealer functionality
    """
    try:
        # Get parameters from event
        username = event.get('username', '')
        password = event.get('password', '')
        return_html = event.get('return_html', False)
        search_for_elements = event.get('search_elements', True)
        search_term = event.get('search_term', '')  # New parameter for search term
        perform_search = event.get('perform_search', False)  # New parameter to trigger search

        print(f"Attempting to login to Georgia DLR portal with username: {username}")

        # Create session with browser-like headers
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        })

        base_url = "https://georgia.dlrdmv.com"
        
        # Step 1: Get the login page to extract form details
        print("Step 1: Getting login page...")
        login_url = f"{base_url}/Account/Login"
        login_page_response = session.get(login_url)
        print(f"Login page status: {login_page_response.status_code}")
        
        if login_page_response.status_code != 200:
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'error': 'Failed to access login page',
                    'status_code': login_page_response.status_code
                })
            }
        
        login_html = login_page_response.text
        
        # Step 2: Extract login form details with enhanced debugging
        print("Step 2: Extracting login form details...")
        
        # First, let's return the HTML to see what we're working with
        if return_html:
            print("DEBUG: Returning login page HTML for analysis")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'debug_mode': True,
                    'login_page_html': login_html,
                    'content_length': len(login_html)
                })
            }
        
        # Find ALL forms on the page
        form_pattern = r'<form[^>]*>(.*?)</form>'
        all_forms = re.findall(form_pattern, login_html, re.IGNORECASE | re.DOTALL)
        print(f"Found {len(all_forms)} forms on the page")
        
        # Find the specific login form with more detailed pattern
        login_form_pattern = r'<form[^>]*(?:action=["\']([^"\']*)["\'])?[^>]*method=["\']([^"\']*)["\'][^>]*>(.*?)</form>'
        detailed_forms = re.findall(login_form_pattern, login_html, re.IGNORECASE | re.DOTALL)
        
        login_form_action = None
        login_form_data = {}
        login_form_method = "POST"
        
        # Analyze each form
        for i, (action, method, form_content) in enumerate(detailed_forms):
            print(f"Form {i+1}: action='{action}', method='{method}'")
            
            # Extract all input fields from this form with more comprehensive pattern
            input_pattern = r'<input[^>]*(?:type=["\']([^"\']*)["\'][^>]*)?(?:name=["\']([^"\']*)["\'][^>]*)?(?:value=["\']([^"\']*)["\'][^>]*)?[^>]*/?>'
            inputs = re.findall(input_pattern, form_content, re.IGNORECASE)
            
            form_fields = {}
            has_email_field = False
            has_password_field = False
            
            # Also try simpler pattern for inputs
            simple_input_pattern = r'<input[^>]+>'
            simple_inputs = re.findall(simple_input_pattern, form_content, re.IGNORECASE)
            
            print(f"Found {len(simple_inputs)} input elements in form {i+1}")
            
            for inp in simple_inputs:
                # Extract attributes from each input
                name_match = re.search(r'name=["\']([^"\']*)["\']', inp, re.IGNORECASE)
                value_match = re.search(r'value=["\']([^"\']*)["\']', inp, re.IGNORECASE)
                type_match = re.search(r'type=["\']([^"\']*)["\']', inp, re.IGNORECASE)
                
                if name_match:
                    field_name = name_match.group(1)
                    field_value = value_match.group(1) if value_match else ''
                    field_type = type_match.group(1) if type_match else 'text'
                    
                    print(f"  Field: {field_name} (type: {field_type}, value: {field_value[:20]}{'...' if len(field_value) > 20 else ''})")
                    
                    # Set appropriate values
                    if field_name.lower() in ['email', 'username', 'user', 'emailaddress']:
                        form_fields[field_name] = username
                        has_email_field = True
                    elif field_name.lower() in ['password', 'pass']:
                        form_fields[field_name] = password
                        has_password_field = True
                    elif field_type.lower() == 'hidden' and field_value:
                        form_fields[field_name] = field_value
                    elif field_name.lower() == 'rememberme':
                        form_fields[field_name] = 'false'
                    else:
                        form_fields[field_name] = field_value
            
            # If this form has both email and password fields, use it
            if has_email_field and has_password_field:
                login_form_action = action if action else "/Account/Login"
                login_form_method = method if method else "POST"
                login_form_data = form_fields
                print(f"✅ Selected form {i+1} as login form")
                print(f"Final form data: {list(login_form_data.keys())}")
                break
        
        # If still no form found, try extracting tokens manually
        if not login_form_data:
            print("No suitable form found, extracting manually...")
            
            # Look for verification token in the entire page
            token_patterns = [
                r'name=["\']__RequestVerificationToken["\'][^>]*value=["\']([^"\']+)["\']',
                r'value=["\']([^"\']+)["\'][^>]*name=["\']__RequestVerificationToken["\']',
                r'"__RequestVerificationToken"[^>]*value=["\']([^"\']+)["\']',
                r'<input[^>]*__RequestVerificationToken[^>]*value=["\']([^"\']+)["\']'
            ]
            
            verification_token = None
            for pattern in token_patterns:
                match = re.search(pattern, login_html, re.IGNORECASE)
                if match:
                    verification_token = match.group(1)
                    print(f"Found verification token: {verification_token[:20]}...")
                    break
            
            login_form_action = "/Account/Login"
            login_form_method = "POST"
            login_form_data = {
                'Email': username,
                'Password': password,
                'RememberMe': 'false'
            }
            
            if verification_token:
                login_form_data['__RequestVerificationToken'] = verification_token
        
        # Step 3: Attempt login
        print("Step 3: Attempting login...")
        
        if not login_form_action.startswith('http'):
            login_post_url = base_url + login_form_action
        else:
            login_post_url = login_form_action
            
        print(f"Posting to: {login_post_url}")
        print(f"Login data keys: {list(login_form_data.keys())}")
        
        # Update headers for form submission
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': login_url
        })
        
        login_response = session.post(login_post_url, data=login_form_data)
        print(f"Login response status: {login_response.status_code}")
        print(f"Login response URL: {login_response.url}")
        
        # Step 4: Check if login was successful and access dashboard
        print("Step 4: Checking login success and accessing dashboard...")
        
        # Try to access dashboard
        dashboard_url = f"{base_url}/#!/dashboard/"
        dashboard_response = session.get(dashboard_url)
        print(f"Dashboard access status: {dashboard_response.status_code}")
        print(f"Dashboard final URL: {dashboard_response.url}")
        
        # Check if we're still on login page
        is_logged_in = 'Account/Login' not in dashboard_response.url
        
        if is_logged_in:
            print("✅ Successfully logged in! Analyzing dashboard...")
            html_content = dashboard_response.text
        else:
            print("❌ Login failed, but analyzing available content...")
            html_content = dashboard_response.text
        
        # Step 5: Enhanced analysis for Angular SPA with dealer functionality
        dealer_search_info = {}
        
        if search_for_elements:
            print("Step 5: Analyzing Angular SPA for dealer functionality...")
            
            try:
                dealer_keywords = ['dealer', 'dealership', 'company', 'business', 'search', 'find', 'license', 'lightning']
                
                dealer_elements = {
                    'dealer_inputs': [],
                    'dealer_selects': [],
                    'dealer_buttons': [],
                    'dealer_links': [],
                    'search_forms': [],
                    'javascript_references': [],
                    'navigation_items': [],
                    'angular_routes': [],
                    'api_endpoints': [],
                    'user_privileges': []
                }
                
                # Extract user privileges (this shows what features are available)
                privilege_pattern = r'USERPRIVILEGES.*?=.*?\[(.*?)\]'
                privilege_match = re.search(privilege_pattern, html_content, re.IGNORECASE | re.DOTALL)
                if privilege_match:
                    privileges_str = privilege_match.group(1)
                    # Extract individual privileges
                    privilege_list = re.findall(r'"([^"]+)"', privileges_str)
                    # Remove duplicates and filter relevant ones
                    unique_privileges = list(set(privilege_list))
                    relevant_privileges = [p for p in unique_privileges if any(keyword in p.lower() for keyword in dealer_keywords + ['lightning', 'inquiry', 'maint'])]
                    dealer_elements['user_privileges'] = relevant_privileges
                    print(f"Found {len(relevant_privileges)} relevant privileges: {relevant_privileges}")
                
                # Look for Angular routes/states in JavaScript
                route_patterns = [
                    r'\.state\([\'"]([^\'\"]+)[\'"],\s*{[^}]*url:\s*[\'"]([^\'\"]*)[\'"]',
                    r'\.when\([\'"]([^\'\"]+)[\'"],\s*{',
                    r'templateUrl:\s*[\'"]([^\'\"]*)[\'"]',
                    r'controller:\s*[\'"]([^\'\"]*)[\'"]'
                ]
                
                for pattern in route_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    if matches:
                        dealer_elements['angular_routes'].extend(matches)
                
                # Look for API endpoints
                api_patterns = [
                    r'[\'"]/(api/[^\'\"]*)[\'"]',
                    r'[\'"]([^\'\"]*api[^\'\"]*)[\'"]',
                    r'url:\s*[\'"]([^\'\"]*(?:search|dealer|inquiry)[^\'\"]*)[\'"]'
                ]
                
                for pattern in api_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    api_endpoints = [match for match in matches if any(keyword in match.lower() for keyword in dealer_keywords + ['api'])]
                    dealer_elements['api_endpoints'].extend(api_endpoints)
                
                # Look for Angular controllers or services
                controller_pattern = r'\.controller\([\'"]([^\'\"]*(?:' + '|'.join(dealer_keywords) + r')[^\'\"]*)[\'"]'
                controllers = re.findall(controller_pattern, html_content, re.IGNORECASE)
                dealer_elements['angular_controllers'] = controllers
                
                # Look for service definitions
                service_pattern = r'\.service\([\'"]([^\'\"]*(?:' + '|'.join(dealer_keywords) + r')[^\'\"]*)[\'"]'
                services = re.findall(service_pattern, html_content, re.IGNORECASE)
                dealer_elements['angular_services'] = services
                
                # Look for menu items or navigation that might lead to dealer functions
                menu_patterns = [
                    r'<[^>]*ng-click=[\'"]([^\'\"]*(?:' + '|'.join(dealer_keywords) + r')[^\'\"]*)[\'"][^>]*>([^<]*)</[^>]*>',
                    r'<a[^>]*href=[\'"]([^\'\"]*(?:' + '|'.join(dealer_keywords) + r')[^\'\"]*)[\'"][^>]*>([^<]*)</a>',
                    r'<li[^>]*>.*?<a[^>]*href=[\'"]([^\'\"]*)[\'"][^>]*>([^<]*(?:' + '|'.join(dealer_keywords) + r')[^<]*)</a>'
                ]
                
                for pattern in menu_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    for href, text in matches:
                        dealer_elements['navigation_items'].append({
                            'text': html.unescape(text.strip()),
                            'href': href,
                            'type': 'menu_item'
                        })
                
                # Look for form templates or input configurations
                ng_model_pattern = r'ng-model=[\'"]([^\'\"]*(?:' + '|'.join(dealer_keywords) + r')[^\'\"]*)[\'"]'
                ng_models = re.findall(ng_model_pattern, html_content, re.IGNORECASE)
                dealer_elements['angular_models'] = ng_models
                
                # Look for function calls that might be dealer-related
                function_pattern = r'function\s+([^(]*(?:' + '|'.join(dealer_keywords) + r')[^(]*)\s*\('
                functions = re.findall(function_pattern, html_content, re.IGNORECASE)
                dealer_elements['javascript_functions'] = functions
                
                # Look for specific lightning search functionality
                lightning_patterns = [
                    r'lightning[^\'\"]*search[^\'\"]*',
                    r'search[^\'\"]*lightning[^\'\"]*',
                    r'lightning.*?{.*?}',
                    r'LIGHTNING[A-Z_]*'
                ]
                
                lightning_refs = []
                for pattern in lightning_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    lightning_refs.extend(matches)
                
                dealer_elements['lightning_search_refs'] = lightning_refs[:10]
                
                # Look for data tables or grids that might show dealer results
                table_patterns = [
                    r'<table[^>]*(?:' + '|'.join(dealer_keywords) + r')[^>]*>',
                    r'ng-table[^>]*',
                    r'ui-grid[^>]*',
                    r'data-table[^>]*'
                ]
                
                table_elements = []
                for pattern in table_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    table_elements.extend(matches)
                
                dealer_elements['data_tables'] = table_elements
                
                dealer_search_info = dealer_elements
                
            except Exception as e:
                print(f"Error parsing Angular SPA: {str(e)}")
                dealer_search_info['parsing_error'] = str(e)
        
        # Step 6: Look for Angular routes or SPA navigation
        angular_routes = []
        if '#!/' in html_content or 'angular' in html_content.lower():
            print("Step 6: Looking for Angular routes...")
            route_patterns = [
                r'when\([\'"]([^\'\"]+)[\'"]',
                r'state\([\'"]([^\'\"]+)[\'"]',
                r'url:\s*[\'"]([^\'\"]+)[\'"]',
                r'#!([^\'\"\\s]+)'
            ]
            
            for pattern in route_patterns:
                matches = re.findall(pattern, html_content)
                angular_routes.extend(matches)
            
            angular_routes = list(set([route.strip() for route in angular_routes if route.strip()]))[:20]

        # Step 7: Perform dealer/deal search if requested
        search_results = {}
        if perform_search and search_term and is_logged_in:
            print(f"Step 7: Performing deal/dealer search for: {search_term}")

            try:
                # Enhanced search endpoints for deal numbers
                search_endpoints = [
                    f"{base_url}/api/Lightning/Search",
                    f"{base_url}/api/search",
                    f"{base_url}/api/dealer/search",
                    f"{base_url}/api/deal/search",
                    f"{base_url}/api/deals/search",
                    f"{base_url}/Lightning/Search",
                    f"{base_url}/search",
                    f"{base_url}/deals",
                    f"{base_url}/api/deals",
                    f"{base_url}/api/lightning/deals"
                ]

                # Enhanced search data variants for deal numbers
                search_data_variants = [
                    {'dealNumber': search_term},
                    {'deal_number': search_term},
                    {'dealId': search_term},
                    {'deal_id': search_term},
                    {'searchTerm': search_term},
                    {'term': search_term},
                    {'query': search_term},
                    {'dealerId': search_term},
                    {'licenseNumber': search_term},
                    {'id': search_term},
                    {'search': search_term},
                    {'number': search_term}
                ]

                # Enhanced query parameters for deal numbers
                search_params_variants = [
                    {'dealNumber': search_term},
                    {'deal_number': search_term},
                    {'dealId': search_term},
                    {'deal_id': search_term},
                    {'searchTerm': search_term},
                    {'term': search_term},
                    {'query': search_term},
                    {'q': search_term},
                    {'id': search_term},
                    {'number': search_term}
                ]
                
                search_success = False
                search_response_data = None
                
                # Update headers for API calls
                session.headers.update({
                    'Content-Type': 'application/json',
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': f"{base_url}/#!/dashboard/"
                })
                
                # Try each endpoint with different data formats
                for endpoint in search_endpoints:
                    if search_success:
                        break
                        
                    print(f"Trying search endpoint: {endpoint}")
                    
                    # Method 1: POST with JSON data
                    for search_data in search_data_variants:
                        try:
                            response = session.post(endpoint, json=search_data, timeout=10)
                            print(f"POST {endpoint} with {search_data} - Status: {response.status_code}")
                            
                            if response.status_code == 200:
                                try:
                                    json_response = response.json()
                                    if json_response and (
                                        isinstance(json_response, list) or 
                                        (isinstance(json_response, dict) and len(json_response) > 0)
                                    ):
                                        search_success = True
                                        search_response_data = json_response
                                        search_results['method'] = f"POST {endpoint}"
                                        search_results['data_sent'] = search_data
                                        print(f"✅ Search successful! Got response with {len(str(json_response))} characters")
                                        break
                                except:
                                    # If not JSON, check if it's meaningful text
                                    if len(response.text) > 50:
                                        search_success = True
                                        search_response_data = response.text
                                        search_results['method'] = f"POST {endpoint} (text response)"
                                        search_results['data_sent'] = search_data
                                        print(f"✅ Search successful! Got text response")
                                        break
                        except Exception as e:
                            print(f"Error with POST {endpoint}: {str(e)}")
                            continue
                    
                    if search_success:
                        break
                    
                    # Method 2: GET with query parameters
                    for search_params in search_params_variants:
                        try:
                            response = session.get(endpoint, params=search_params, timeout=10)
                            print(f"GET {endpoint} with params {search_params} - Status: {response.status_code}")
                            
                            if response.status_code == 200:
                                try:
                                    json_response = response.json()
                                    if json_response and (
                                        isinstance(json_response, list) or 
                                        (isinstance(json_response, dict) and len(json_response) > 0)
                                    ):
                                        search_success = True
                                        search_response_data = json_response
                                        search_results['method'] = f"GET {endpoint}"
                                        search_results['params_sent'] = search_params
                                        print(f"✅ Search successful! Got response")
                                        break
                                except:
                                    if len(response.text) > 50:
                                        search_success = True
                                        search_response_data = response.text
                                        search_results['method'] = f"GET {endpoint} (text response)"
                                        search_results['params_sent'] = search_params
                                        print(f"✅ Search successful! Got text response")
                                        break
                        except Exception as e:
                            print(f"Error with GET {endpoint}: {str(e)}")
                            continue
                    
                    if search_success:
                        break
                
                # Store search results with enhanced HTML parsing for deal data
                if search_success:
                    search_results['success'] = True
                    search_results['search_term'] = search_term
                    search_results['response_data'] = search_response_data
                    search_results['response_type'] = type(search_response_data).__name__
                    search_results['response_length'] = len(str(search_response_data))

                    # Parse HTML response if it's HTML format
                    if isinstance(search_response_data, str) and '<html' in search_response_data.lower():
                        search_results['parsed_html_data'] = parse_deal_html_response(search_response_data, search_term)
                    elif isinstance(search_response_data, str) and ('<table' in search_response_data.lower() or '<tr' in search_response_data.lower()):
                        search_results['parsed_table_data'] = parse_deal_table_response(search_response_data, search_term)
                else:
                    search_results['success'] = False
                    search_results['search_term'] = search_term
                    search_results['message'] = 'No search endpoints responded successfully'
                    search_results['endpoints_tried'] = search_endpoints
                
            except Exception as e:
                print(f"Error performing search: {str(e)}")
                search_results['success'] = False
                search_results['error'] = str(e)
        
        elif perform_search and search_term and not is_logged_in:
            search_results = {
                'success': False,
                'error': 'Cannot perform search - not logged in'
            }
        
        elif perform_search and not search_term:
            search_results = {
                'success': False,
                'error': 'No search term provided'
            }
        
        # Prepare response
        response_data = {
            'success': True,
            'login_attempted': True,
            'is_logged_in': is_logged_in,
            'url_accessed': dashboard_response.url,
            'status_code': dashboard_response.status_code,
            'content_length': len(html_content),
            'dealer_search_elements': dealer_search_info,
            'angular_routes_found': angular_routes,
            'search_results': search_results,  # Include search results
            'page_analysis': {
                'has_angular': 'angular' in html_content.lower() or '#!/' in html_content,
                'has_forms': '<form' in html_content.lower(),
                'has_search_inputs': 'search' in html_content.lower(),
                'has_dealer_references': any(keyword in html_content.lower() for keyword in ['dealer', 'dealership', 'license']),
                'page_title': re.search(r'<title>([^<]*)</title>', html_content, re.IGNORECASE).group(1) if re.search(r'<title>([^<]*)</title>', html_content, re.IGNORECASE) else 'No title found'
            }
        }
        
        # Include HTML content if requested
        if return_html:
            response_data['html_content'] = html_content
        
        return {
            'statusCode': 200,
            'body': json.dumps(response_data, indent=2)
        }

    except Exception as e:
        print(f"Lambda execution error: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Internal server error',
                'message': str(e)
            })
        }