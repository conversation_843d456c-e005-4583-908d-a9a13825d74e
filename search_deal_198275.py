#!/usr/bin/env python3
"""
Simple script to search for Deal ID 198275 in Georgia DLR portal
"""

import json
import sys
import os

# Add the current directory to Python path to import lambda function
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lambda_function import lambda_handler

def search_deal_198275(username, password):
    """
    Search for deal ID 198275 specifically
    
    Args:
        username (str): Your Georgia DLR portal username
        password (str): Your Georgia DLR portal password
    
    Returns:
        dict: Search results including parsed HTML data
    """
    
    # Configuration for searching deal 198275
    search_config = {
        'username': username,
        'password': password,
        'perform_search': True,
        'search_term': '198275',
        'return_html': False,
        'search_elements': True
    }
    
    print("🔍 Searching for Deal ID: 198275")
    print("=" * 40)
    
    try:
        # Execute the search
        result = lambda_handler(search_config, None)
        
        if result['statusCode'] == 200:
            body = json.loads(result['body'])
            
            # Check login status
            if not body.get('is_logged_in'):
                print("❌ Login failed. Please check your credentials.")
                return None
            
            print("✅ Successfully logged in to Georgia DLR portal")
            
            # Check search results
            search_results = body.get('search_results', {})
            
            if search_results.get('success'):
                print("✅ Search completed successfully")
                print(f"📊 Search method: {search_results.get('method', 'Unknown')}")
                print(f"📄 Response type: {search_results.get('response_type', 'Unknown')}")
                print(f"📏 Response length: {search_results.get('response_length', 0):,} characters")
                
                # Process HTML response if available
                if 'parsed_html_data' in search_results:
                    html_data = search_results['parsed_html_data']
                    deals_found = html_data.get('total_deals', 0)
                    
                    print(f"\n📋 HTML Response Analysis:")
                    print(f"   Deals found: {deals_found}")
                    
                    if deals_found > 0:
                        print(f"   Deal details:")
                        for i, deal in enumerate(html_data.get('deals_found', [])[:3]):
                            print(f"     Deal {i+1}:")
                            if 'cells' in deal:
                                print(f"       Table cells: {deal['cells']}")
                            if 'text' in deal:
                                print(f"       Text content: {deal['text']}")
                            print()
                
                # Process table response if available
                if 'parsed_table_data' in search_results:
                    table_data = search_results['parsed_table_data']
                    matching_rows = table_data.get('matching_rows', [])
                    
                    print(f"📊 Table Response Analysis:")
                    print(f"   Headers: {table_data.get('headers', [])}")
                    print(f"   Total rows: {len(table_data.get('table_data', []))}")
                    print(f"   Matching rows: {len(matching_rows)}")
                    
                    if matching_rows:
                        print(f"   Matching row details:")
                        for i, row in enumerate(matching_rows[:2]):
                            print(f"     Row {i+1}: {row['cells']}")
                        print()
                
                # Show raw response data (truncated)
                response_data = search_results.get('response_data')
                if response_data:
                    print(f"📝 Raw Response Preview:")
                    if isinstance(response_data, str):
                        # HTML response
                        if '<html' in response_data.lower():
                            print("   Response type: HTML document")
                            # Look for specific deal information
                            if '198275' in response_data:
                                print("   ✅ Deal ID 198275 found in response!")
                            else:
                                print("   ⚠️  Deal ID 198275 not found in response")
                        else:
                            print(f"   Response preview: {response_data[:200]}...")
                    
                    elif isinstance(response_data, (dict, list)):
                        # JSON response
                        print("   Response type: JSON data")
                        print(f"   Data preview: {json.dumps(response_data, indent=2)[:300]}...")
                
                return {
                    'success': True,
                    'search_results': search_results,
                    'full_response': body
                }
            
            else:
                print("❌ Search failed")
                error_msg = search_results.get('error', 'Unknown error')
                print(f"   Error: {error_msg}")
                
                if 'endpoints_tried' in search_results:
                    print(f"   Endpoints attempted: {len(search_results['endpoints_tried'])}")
                    for endpoint in search_results['endpoints_tried'][:3]:
                        print(f"     - {endpoint}")
                
                return {
                    'success': False,
                    'error': error_msg,
                    'search_results': search_results
                }
        
        else:
            print(f"❌ Request failed with status code: {result['statusCode']}")
            error_body = json.loads(result['body'])
            print(f"   Error: {error_body.get('error', 'Unknown error')}")
            return None
    
    except Exception as e:
        print(f"❌ Execution failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    Main function - replace with your actual credentials
    """
    
    # REPLACE THESE WITH YOUR ACTUAL CREDENTIALS
    USERNAME = "<EMAIL>"
    PASSWORD = "your_password"
    
    if USERNAME == "<EMAIL>" or PASSWORD == "your_password":
        print("⚠️  Please update the USERNAME and PASSWORD variables with your actual credentials")
        print("   Edit this file and replace the placeholder values")
        return
    
    # Perform the search
    result = search_deal_198275(USERNAME, PASSWORD)
    
    if result and result.get('success'):
        print("\n🎉 Search completed successfully!")
        print("   Check the output above for deal information")
    else:
        print("\n💥 Search failed or returned no results")
        print("   Check your credentials and network connection")

if __name__ == "__main__":
    main()
