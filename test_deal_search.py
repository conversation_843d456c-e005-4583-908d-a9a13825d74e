#!/usr/bin/env python3
"""
Test script for searching deal ID 198275 in Georgia DLR portal
This script demonstrates how to use the enhanced lambda function to search for deals
"""

import json
from lambda_function import lambda_handler, test_deal_search

def main():
    """
    Main test function for deal search
    """
    print("Georgia DLR Deal Search Test")
    print("=" * 40)
    print("Testing search for Deal ID: 198275")
    print()
    
    # Test configuration
    test_config = {
        'username': '<EMAIL>',  # Replace with your actual username
        'password': 'your_password',              # Replace with your actual password
        'perform_search': True,
        'search_term': '198275',
        'return_html': False,  # Set to True if you want to see the raw HTML
        'search_elements': True
    }
    
    print("Configuration:")
    print(f"  Username: {test_config['username']}")
    print(f"  Search Term: {test_config['search_term']}")
    print(f"  Return HTML: {test_config['return_html']}")
    print()
    
    try:
        # Execute the search
        print("Executing search...")
        result = lambda_handler(test_config, None)
        
        # Parse results
        if result['statusCode'] == 200:
            body = json.loads(result['body'])
            
            print("Results:")
            print("-" * 20)
            print(f"Login Status: {'✅ Successful' if body.get('is_logged_in') else '❌ Failed'}")
            print(f"URL Accessed: {body.get('url_accessed', 'N/A')}")
            print(f"Status Code: {body.get('status_code', 'N/A')}")
            print(f"Content Length: {body.get('content_length', 0):,} characters")
            print()
            
            # Search results
            search_results = body.get('search_results', {})
            if search_results:
                print("Search Results:")
                print("-" * 15)
                print(f"Search Success: {'✅ Yes' if search_results.get('success') else '❌ No'}")
                
                if search_results.get('success'):
                    print(f"Search Method: {search_results.get('method', 'Unknown')}")
                    print(f"Response Type: {search_results.get('response_type', 'Unknown')}")
                    print(f"Response Length: {search_results.get('response_length', 0):,} characters")
                    
                    # Display parsed HTML data
                    if 'parsed_html_data' in search_results:
                        html_data = search_results['parsed_html_data']
                        print(f"\nParsed HTML Data:")
                        print(f"  Total Deals Found: {html_data.get('total_deals', 0)}")
                        
                        deals = html_data.get('deals_found', [])
                        if deals:
                            print(f"  Deal Details:")
                            for i, deal in enumerate(deals[:5]):  # Show first 5 deals
                                print(f"    Deal {i+1}:")
                                if 'cells' in deal:
                                    print(f"      Cells: {deal['cells']}")
                                if 'text' in deal:
                                    print(f"      Text: {deal['text']}")
                                if 'type' in deal:
                                    print(f"      Type: {deal['type']}")
                                print()
                    
                    # Display parsed table data
                    if 'parsed_table_data' in search_results:
                        table_data = search_results['parsed_table_data']
                        print(f"\nParsed Table Data:")
                        print(f"  Headers: {table_data.get('headers', [])}")
                        print(f"  Total Rows: {len(table_data.get('table_data', []))}")
                        print(f"  Matching Rows: {len(table_data.get('matching_rows', []))}")
                        
                        matching_rows = table_data.get('matching_rows', [])
                        if matching_rows:
                            print(f"  Matching Row Details:")
                            for i, row in enumerate(matching_rows[:3]):  # Show first 3 matching rows
                                print(f"    Row {i+1}: {row['cells']}")
                    
                    # Display raw response data (truncated)
                    if 'response_data' in search_results:
                        response_data = search_results['response_data']
                        if isinstance(response_data, str):
                            print(f"\nRaw Response (first 500 chars):")
                            print(response_data[:500] + "..." if len(response_data) > 500 else response_data)
                        elif isinstance(response_data, (dict, list)):
                            print(f"\nJSON Response:")
                            print(json.dumps(response_data, indent=2)[:1000] + "..." if len(str(response_data)) > 1000 else json.dumps(response_data, indent=2))
                
                else:
                    print(f"Search Failed: {search_results.get('error', 'Unknown error')}")
                    if 'endpoints_tried' in search_results:
                        print(f"Endpoints Tried: {search_results['endpoints_tried']}")
            
            # Page analysis
            page_analysis = body.get('page_analysis', {})
            if page_analysis:
                print(f"\nPage Analysis:")
                print(f"  Has Angular: {page_analysis.get('has_angular', False)}")
                print(f"  Has Forms: {page_analysis.get('has_forms', False)}")
                print(f"  Has Search Inputs: {page_analysis.get('has_search_inputs', False)}")
                print(f"  Has Dealer References: {page_analysis.get('has_dealer_references', False)}")
                print(f"  Page Title: {page_analysis.get('page_title', 'N/A')}")
        
        else:
            print(f"❌ Request failed with status code: {result['statusCode']}")
            error_body = json.loads(result['body'])
            print(f"Error: {error_body.get('error', 'Unknown error')}")
            print(f"Message: {error_body.get('message', 'No message')}")
    
    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        import traceback
        traceback.print_exc()

def test_with_html_return():
    """
    Test function that returns HTML for debugging
    """
    print("\nTesting with HTML return for debugging...")
    
    test_config = {
        'username': '<EMAIL>',
        'password': 'your_password',
        'return_html': True,  # This will return the login page HTML
        'search_elements': False
    }
    
    result = lambda_handler(test_config, None)
    
    if result['statusCode'] == 200:
        body = json.loads(result['body'])
        if 'login_page_html' in body:
            html_content = body['login_page_html']
            print(f"Login page HTML length: {len(html_content):,} characters")
            print("First 1000 characters:")
            print(html_content[:1000])
            print("...")
        elif 'html_content' in body:
            html_content = body['html_content']
            print(f"Dashboard HTML length: {len(html_content):,} characters")
            print("First 1000 characters:")
            print(html_content[:1000])
            print("...")

if __name__ == "__main__":
    # Run the main test
    main()
    
    # Uncomment the line below to also test HTML return
    # test_with_html_return()
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print("Remember to replace '<EMAIL>' and 'your_password'")
    print("with your actual Georgia DLR portal credentials.")
